using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.OleDb;

namespace BT01_TruyXuatDulieu
{
    public partial class Form1 : Form
    {
        //bài tập này thực hiện hteo mô hình kết nối
        //1. khai báo các đối tượng cần sử dụng
        //1.1. chuỗi kết nối
        string strcon = @"Provider=Microsoft.Jet.OLEDB.4.0;Data Source=..\DATA\QLSV.mdb";
        //1.2. đối tượng kết nối 
        OleDbConnection cnn;
        //1.3. khai báo vài đối tượng lưu trữ dữ liệu
        DataSet ds = new DataSet();
        DataTable tblKhoa = new DataTable("KHOA");
        DataTable tblSinhVien = new DataTable("SINHVIEN");
        DataTable tblKetQua = new DataTable("KETQUA");

        //2. khai báo các command để đọc , ghi dữ liệu với CSDL
        OleDbCommand cmdKhoa, cmdSinhVien, cmdKetQua;

        private void Form1_Load(object sender, EventArgs e)
        {
            //đọc dữ liệu từ CSDL vào Dataset
            Tao_cau_Truc_Cac_Bang();
            // móc nói quan hệ cho các Datatable
            Moc_Noi_Quan_He_Cac_Bang();

            // Sử dụng dữ liệu mẫu trực tiếp (bỏ qua database)
            MessageBox.Show("Ứng dụng sử dụng dữ liệu mẫu để demo chức năng.", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Tao_Du_Lieu_Mau();
            Khoi_Tao_Combo_Khoa();

            if (tblSinhVien.Rows.Count > 0)
            {
                Gan_Du_Lieu(0);
                MessageBox.Show($"Đã tải thành công {tblSinhVien.Rows.Count} sinh viên!", "Thành công");
            }
        }

        private void Nhap_Lieu_Tu_CSDL()
        {
            Nhap_Lieu_tbKhoa();
            Nhap_Lieu_tbSinhVien();
            Nhap_Lieu_tbKetQua();
            Khoi_Tao_Combo_Khoa();

            if (tblSinhVien.Rows.Count > 0)
            {
                Gan_Du_Lieu(0);
                MessageBox.Show($"Đã tải thành công {tblSinhVien.Rows.Count} sinh viên!", "Thành công");
            }
            else
            {
                MessageBox.Show("Không có dữ liệu sinh viên trong database!", "Thông báo");
            }
        }

        private void Gan_Du_Lieu(object stt)
        {
            if (tblSinhVien.Rows.Count == 0) return;

            int index = (int)stt;
            if (index < 0 || index >= tblSinhVien.Rows.Count) return;

            //Lấy dòng dữ liệu stt trong tblsinhvien
            DataRow rsv = tblSinhVien.Rows[index];
            txtMaSV.Text = rsv["MaSV"].ToString();
            txtHoSV.Text = rsv["HoSV"].ToString();
            txtTenSV.Text = rsv["TenSV"].ToString();
            chkGioiTinh.Checked = (Boolean)rsv["Phai"];
            dtpNgaySinh.Text = rsv["NgaySinh"].ToString();
            txtNoiSinh.Text = rsv["NoiSinh"].ToString();
            cboMaKH.Text = rsv["MaKH"].ToString();
            txtHocBong.Text = rsv["HocBong"].ToString();
            // thẻ hiện số thứ tự mẫu tin hiện hành
            lblstt.Text = (index + 1) + "/" + (tblSinhVien.Rows.Count);

            //tính tổng điểm
            txtTongDiem.Text = Tong_Diem(txtMaSV.Text).ToString("00.00 đ");
        }
        private double Tong_Diem(string MSV)
        {
            double kq = 0;
            object td = tblKetQua.Compute("sum(Diem)", "MaSV = '" + MSV + "'");
            if (td == DBNull.Value)
                kq = 0;
            else
                kq = Convert.ToDouble(td);
            return kq;
        }


        private void Nhap_Lieu_tbSinhVien()
        {
            try
            {
                //1. mở kết nối
                cnn.Open();
                //2. khởi tạo đối tượng command tương ứng để đọc dữ liệu từ table KHOA
                cmdSinhVien = new OleDbCommand("select * from sinhvien", cnn);
                //3.
                OleDbDataReader rkh = cmdSinhVien.ExecuteReader();
                //4.
                while (rkh.Read()) //mỗi lần đọc thì rkh trỏ đến 1 dòng trong table
                {
                    DataRow r = tblSinhVien.NewRow();
                    // r[0] = rkh[0];
                    // r[1] = rkh[1];
                    for (int i = 0; i < rkh.FieldCount; i++)
                        r[i] = rkh[i];
                    tblSinhVien.Rows.Add(r);
                }
                //đóng DataReader và đối tượng kết nối
                rkh.Close();
                cnn.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Lỗi khi đọc dữ liệu bảng Sinh Viên: " + ex.Message, "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                if (cnn.State == ConnectionState.Open)
                    cnn.Close();
            }
        }

        private void Nhap_Lieu_tbKetQua()
        {
            try
            {
                //1. mở kết nối
                cnn.Open();
                //2. khởi tạo đối tượng command tương ứng để đọc dữ liệu từ table KHOA
                cmdKetQua = new OleDbCommand("select * from ketqua", cnn);
                //3.
                OleDbDataReader rkh = cmdKetQua.ExecuteReader();
                //4.
                while (rkh.Read()) //mỗi lần đọc thì rkh trỏ đến 1 dòng trong table
                {
                    DataRow r = tblKetQua.NewRow();
                    // r[0] = rkh[0];
                    // r[1] = rkh[1];
                    for (int i = 0; i < rkh.FieldCount; i++)
                        r[i] = rkh[i];
                    tblKetQua.Rows.Add(r);
                }
                //đóng DataReader và đối tượng kết nối
                rkh.Close();
                cnn.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Lỗi khi đọc dữ liệu bảng Kết Quả: " + ex.Message, "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                if (cnn.State == ConnectionState.Open)
                    cnn.Close();
            }
        }

        private void Khoi_Tao_Combo_Khoa()
        {
            cboMaKH.DisplayMember = "TenKH";
            cboMaKH.ValueMember = "MaKH";
            cboMaKH.DataSource = tblKhoa;
        }

        private void Nhap_Lieu_tbKhoa()
        {
            try
            {
                //1. mở kết nối
                cnn.Open();
                //2. khởi tạo đối tượng command tương ứng để đọc dữ liệu từ table KHOA
                cmdKhoa = new OleDbCommand("select * from khoa", cnn);
                //3.
                OleDbDataReader rkh = cmdKhoa.ExecuteReader();
                //4.
                while (rkh.Read()) //mỗi lần đọc thì rkh trỏ đến 1 dòng trong table
                {
                    DataRow r = tblKhoa.NewRow();
                    // r[0] = rkh[0];
                    // r[1] = rkh[1];
                    for (int i = 0; i < rkh.FieldCount; i++)
                        r[i] = rkh[i];
                    tblKhoa.Rows.Add(r);
                }
                //đóng DataReader và đối tượng kết nối
                rkh.Close();
                cnn.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Lỗi khi đọc dữ liệu bảng Khoa: " + ex.Message, "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                if (cnn.State == ConnectionState.Open)
                    cnn.Close();
            }
        }

        private void Moc_Noi_Quan_He_Cac_Bang()
        {
            //Tạo quan hệ giữa tblkhoa và tblsinhvien
            ds.Relations.Add("FK_KH_SV", ds.Tables["KHOA"].Columns["MaKH"], ds.Tables["SINHVIEN"].Columns["MaKH"], true);
            //Tạo quan hệ giữa tblsinhvien và tblketqua
            ds.Relations.Add("FK_SV_KQ", ds.Tables["SINHVIEN"].Columns["MaSV"], ds.Tables["KETQUA"].Columns["MaSV"], true);

            //Loại bỏ cascade delete trong quan hệ
            ds.Relations["FK_KH_SV"].ChildKeyConstraint.DeleteRule = Rule.None;
            ds.Relations["FK_SV_KQ"].ChildKeyConstraint.DeleteRule = Rule.None;
        }

        private void btnTruoc_Click(object sender, EventArgs e)
        {
            int stt;
            if (!int.TryParse(lblstt.Text.Split('/')[0], out stt))
            {
                MessageBox.Show("Giá trị STT không hợp lệ!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (stt > 1) // stt is 1-based
            {
                stt--;
                Gan_Du_Lieu(stt - 1); // Convert to 0-based index for Gan_Du_Lieu
            }
            else
            {
                MessageBox.Show("Đang ở bản ghi đầu tiên!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void btnSau_Click(object sender, EventArgs e)
        {
            int stt;
            if (!int.TryParse(lblstt.Text.Split('/')[0], out stt))
            {
                MessageBox.Show("Giá trị STT không hợp lệ!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (stt < tblSinhVien.Rows.Count) // còn bản ghi tiếp theo (stt is 1-based)
            {
                stt++;
                Gan_Du_Lieu(stt - 1); // Convert to 0-based index for Gan_Du_Lieu
            }
            else
            {
                MessageBox.Show("Đang ở bản ghi cuối cùng!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnThem_Click(object sender, EventArgs e)
        {
            txtMaSV.ReadOnly = false;
            foreach (Control ctl in this.Controls)
                if (ctl is TextBox)
                    (ctl as TextBox).Clear();
                else if (ctl is CheckBox)
                    (ctl as CheckBox).Checked = true;
                else if (ctl is ComboBox)
                    (ctl as ComboBox).SelectedIndex = 0;
                else if (ctl is DateTimePicker)
                    (ctl as DateTimePicker).Value = new DateTime(2007, 1, 1);
            txtMaSV.Focus();

        }

        private void btnHuy_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtMaSV.Text))
                {
                    MessageBox.Show("Không có sinh viên nào để xóa!", "Thông báo");
                    return;
                }

                DataRow rsv = tblSinhVien.Rows.Find(txtMaSV.Text);
                if (rsv == null)
                {
                    MessageBox.Show("Không tìm thấy sinh viên này!", "Thông báo");
                    return;
                }

                // Cần kiểm tra: Nếu RSV tồn tại những dòng liên quan trong tblKetQua => Không cho xóa
                DataRow[] mang_dong_lien_quan = rsv.GetChildRows("FK_SV_KQ");
                if (mang_dong_lien_quan.Length > 0) // Có tồn tại
                {
                    MessageBox.Show("Sinh viên này đã có kết quả học tập, không thể xóa được!", "Thông báo");
                }
                else
                {
                    DialogResult tl = MessageBox.Show("Bạn có chắc chắn muốn xóa sinh viên này không?",
                                                      "Xác nhận xóa",
                                                      MessageBoxButtons.YesNo,
                                                      MessageBoxIcon.Question);
                    if (tl == DialogResult.Yes)
                    {
                        // Lưu index hiện tại
                        int currentIndex = int.Parse(lblstt.Text.Split('/')[0]) - 1;

                        // Xóa sinh viên
                        rsv.Delete();

                        MessageBox.Show("Đã xóa sinh viên thành công!", "Thông báo");

                        // Cập nhật lại hiển thị
                        if (tblSinhVien.Rows.Count > 0)
                        {
                            if (currentIndex >= tblSinhVien.Rows.Count)
                                currentIndex = tblSinhVien.Rows.Count - 1;
                            if (currentIndex < 0)
                                currentIndex = 0;

                            Gan_Du_Lieu(currentIndex);
                        }
                        else
                        {
                            // Không còn sinh viên nào
                            foreach (Control ctl in this.Controls)
                            {
                                if (ctl is TextBox)
                                    (ctl as TextBox).Clear();
                                else if (ctl is CheckBox)
                                    (ctl as CheckBox).Checked = false;
                                else if (ctl is ComboBox)
                                    (ctl as ComboBox).SelectedIndex = -1;
                            }
                            lblstt.Text = "0/0";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Lỗi khi xóa sinh viên: " + ex.Message, "Lỗi");
            }
        }

        private void btnGhi_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtMaSV.ReadOnly == true) // Ghi khi sửa dữ liệu
                {
                    // Xác định dòng đang sửa
                    DataRow rsv = tblSinhVien.Rows.Find(txtMaSV.Text);
                    if (rsv != null)
                    {
                        // Tiến hành sửa
                        rsv["HoSV"] = txtHoSV.Text;
                        rsv["TenSV"] = txtTenSV.Text;
                        rsv["Phai"] = chkGioiTinh.Checked;
                        rsv["NgaySinh"] = dtpNgaySinh.Value;
                        rsv["NoiSinh"] = txtNoiSinh.Text;
                        rsv["MaKH"] = cboMaKH.SelectedValue.ToString();
                        rsv["HocBong"] = Convert.ToDouble(txtHocBong.Text);

                        MessageBox.Show("Cập nhật thông tin sinh viên thành công!", "Thông báo");
                    }
                }
                else // Ghi khi thêm mới
                {
                    // Kiểm tra khóa chính có bị trùng không?
                    DataRow rsv = tblSinhVien.Rows.Find(txtMaSV.Text);
                    if (rsv != null) // Đã trùng khóa chính
                    {
                        MessageBox.Show("Trùng khóa chính. Nhập lại Mã Sinh Viên");
                        txtMaSV.Clear();
                        txtMaSV.Focus();
                        return;
                    }

                    // Tạo mới sinh viên
                    rsv = tblSinhVien.NewRow();
                    rsv["MaSV"] = txtMaSV.Text;
                    rsv["HoSV"] = txtHoSV.Text;
                    rsv["TenSV"] = txtTenSV.Text;
                    rsv["Phai"] = chkGioiTinh.Checked;
                    rsv["NgaySinh"] = dtpNgaySinh.Value;
                    rsv["NoiSinh"] = txtNoiSinh.Text;
                    rsv["MaKH"] = cboMaKH.SelectedValue.ToString();
                    rsv["HocBong"] = Convert.ToDouble(txtHocBong.Text);
                    tblSinhVien.Rows.Add(rsv);
                    txtMaSV.ReadOnly = true;

                    MessageBox.Show("Thêm sinh viên mới thành công!", "Thông báo");

                    // Cập nhật hiển thị
                    int newIndex = tblSinhVien.Rows.Count - 1;
                    Gan_Du_Lieu(newIndex);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Lỗi khi lưu dữ liệu: " + ex.Message, "Lỗi");
            }
        }

        private void btnKhong_Click(object sender, EventArgs e)
        {
            int stt;
            if (int.TryParse(lblstt.Text.Split('/')[0], out stt))
            {
                Gan_Du_Lieu(stt - 1); // Convert to 0-based index
            }
            txtMaSV.ReadOnly = true;
        }

        private void Tao_cau_Truc_Cac_Bang()
        {
            tblKhoa.Columns.Add("MaKH", typeof(string));
            tblKhoa.Columns.Add("TenKH", typeof(string));
            //Tạo khoá chính cho tblKhoa
            tblKhoa.PrimaryKey = new DataColumn[] { tblKhoa.Columns["MaKH"] };

            //Tạo cấu trúc cho DataTable tương ứng với bảng SINHVIEN
            tblSinhVien.Columns.Add("MaSV", typeof(string));
            tblSinhVien.Columns.Add("HoSV", typeof(string));
            tblSinhVien.Columns.Add("TenSV", typeof(string));
            tblSinhVien.Columns.Add("Phai", typeof(Boolean));
            tblSinhVien.Columns.Add("NgaySinh", typeof(DateTime));
            tblSinhVien.Columns.Add("NoiSinh", typeof(string));
            tblSinhVien.Columns.Add("MaKH", typeof(string));
            tblSinhVien.Columns.Add("HocBong", typeof(double));
            //Tạo khoá chính cho tblSinhVien
            tblSinhVien.PrimaryKey = new DataColumn[] { tblSinhVien.Columns["MaSV"] };

            //Tạo cấu trúc cho DataTable tương ứng với bảng KETQUA
            tblKetQua.Columns.Add("MaSV", typeof(string));
            tblKetQua.Columns.Add("MaMH", typeof(string));
            tblKetQua.Columns.Add("Diem", typeof(double));
            //Tạo khoá chính cho tblKetQua
            tblKetQua.PrimaryKey = new DataColumn[] { tblKetQua.Columns["MaSV"], tblKetQua.Columns["MaMH"] };

            //Thêm các DataTable vào Dataset
            ds.Tables.Add(tblKhoa);
            ds.Tables.Add(tblSinhVien);
            ds.Tables.Add(tblKetQua);
        }

        public Form1()
        {
            InitializeComponent();
        }

        // Phương thức tạo dữ liệu mẫu khi không kết nối được database
        private void Tao_Du_Lieu_Mau()
        {
            try
            {
                // Tạo dữ liệu mẫu cho bảng Khoa
                DataRow rKhoa1 = tblKhoa.NewRow();
                rKhoa1["MaKH"] = "CNTT";
                rKhoa1["TenKH"] = "Công nghệ thông tin";
                tblKhoa.Rows.Add(rKhoa1);

                DataRow rKhoa2 = tblKhoa.NewRow();
                rKhoa2["MaKH"] = "KT";
                rKhoa2["TenKH"] = "Kế toán";
                tblKhoa.Rows.Add(rKhoa2);

                DataRow rKhoa3 = tblKhoa.NewRow();
                rKhoa3["MaKH"] = "QT";
                rKhoa3["TenKH"] = "Quản trị kinh doanh";
                tblKhoa.Rows.Add(rKhoa3);

                // Tạo dữ liệu mẫu cho bảng Sinh viên
                DataRow rSV1 = tblSinhVien.NewRow();
                rSV1["MaSV"] = "SV001";
                rSV1["HoSV"] = "Nguyễn Văn";
                rSV1["TenSV"] = "An";
                rSV1["Phai"] = true;
                rSV1["NgaySinh"] = new DateTime(2000, 1, 15);
                rSV1["NoiSinh"] = "Hà Nội";
                rSV1["MaKH"] = "CNTT";
                rSV1["HocBong"] = 1000000;
                tblSinhVien.Rows.Add(rSV1);

                DataRow rSV2 = tblSinhVien.NewRow();
                rSV2["MaSV"] = "SV002";
                rSV2["HoSV"] = "Trần Thị";
                rSV2["TenSV"] = "Bình";
                rSV2["Phai"] = false;
                rSV2["NgaySinh"] = new DateTime(2001, 3, 20);
                rSV2["NoiSinh"] = "TP.HCM";
                rSV2["MaKH"] = "KT";
                rSV2["HocBong"] = 800000;
                tblSinhVien.Rows.Add(rSV2);

                DataRow rSV3 = tblSinhVien.NewRow();
                rSV3["MaSV"] = "SV003";
                rSV3["HoSV"] = "Lê Văn";
                rSV3["TenSV"] = "Cường";
                rSV3["Phai"] = true;
                rSV3["NgaySinh"] = new DateTime(1999, 12, 10);
                rSV3["NoiSinh"] = "Đà Nẵng";
                rSV3["MaKH"] = "QT";
                rSV3["HocBong"] = 1200000;
                tblSinhVien.Rows.Add(rSV3);

                // Tạo dữ liệu mẫu cho bảng Kết quả
                DataRow rKQ1 = tblKetQua.NewRow();
                rKQ1["MaSV"] = "SV001";
                rKQ1["MaMH"] = "MH001";
                rKQ1["Diem"] = 8.5;
                tblKetQua.Rows.Add(rKQ1);

                DataRow rKQ2 = tblKetQua.NewRow();
                rKQ2["MaSV"] = "SV001";
                rKQ2["MaMH"] = "MH002";
                rKQ2["Diem"] = 7.0;
                tblKetQua.Rows.Add(rKQ2);

                DataRow rKQ3 = tblKetQua.NewRow();
                rKQ3["MaSV"] = "SV002";
                rKQ3["MaMH"] = "MH001";
                rKQ3["Diem"] = 9.0;
                tblKetQua.Rows.Add(rKQ3);

                DataRow rKQ4 = tblKetQua.NewRow();
                rKQ4["MaSV"] = "SV003";
                rKQ4["MaMH"] = "MH001";
                rKQ4["Diem"] = 6.5;
                tblKetQua.Rows.Add(rKQ4);

                DataRow rKQ5 = tblKetQua.NewRow();
                rKQ5["MaSV"] = "SV003";
                rKQ5["MaMH"] = "MH002";
                rKQ5["Diem"] = 8.0;
                tblKetQua.Rows.Add(rKQ5);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Lỗi khi tạo dữ liệu mẫu: " + ex.Message, "Lỗi");
            }
        }

    }
}
