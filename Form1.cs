using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.OleDb;

namespace BT01_TruyXuatDulieu
{
    public partial class Form1 : Form
    {
        //bài tập này thực hiện hteo mô hình kết nối
        //1. khai báo các đối tượng cần sử dụng
        //1.1. chuỗi kết nối
        string strcon = @"Provider = Microsoft.ACE.oledb.12.0; Data Source= ..\DATA\qlsv.mdb";
        //1.2. đối tượng kết nối 
        OleDbConnection cnn;
        //1.3. khai báo vài đối tượng lưu trữ dữ liệu
        DataSet ds = new DataSet();
        DataTable tblKhoa = new DataTable("KHOA");
        DataTable tblSinhVien = new DataTable("SINHVIEN");
        DataTable tblKetQua = new DataTable("KETQUA");

        //2. khai báo các command để đọc , ghi dữ liệu với CSDL
        OleDbCommand cmdKhoa, cmdSinhVien, cmdKetQua;

        private void Form1_Load(object sender, EventArgs e)
        {
            //khởi tạo kết nối
            cnn = new OleDbConnection(strcon);
            //đọc dữ liệu từ CSDL vào Dataset
            Tao_cau_Truc_Cac_Bang();
            // móc nói quan hệ cho các Datatable
            Moc_Noi_Quan_He_Cac_Bang();

            Nhap_Lieu_Tu_CSDL();

        }

        private void Nhap_Lieu_Tu_CSDL()
        {
            Nhap_Lieu_tbKhoa();
            Nhap_Lieu_tbSinhVien();
            Nhap_Lieu_tbKetQua();
            Khoi_Tao_Combo_Khoa();
            if (tblSinhVien.Rows.Count > 0)
            {
                Gan_Du_Lieu(0);
            }

        }

        private void Gan_Du_Lieu(object stt)
        {
            if (tblSinhVien.Rows.Count == 0) return;

            int index = (int)stt;
            if (index < 0 || index >= tblSinhVien.Rows.Count) return;

            //Lấy dòng dữ liệu stt trong tblsinhvien
            DataRow rsv = tblSinhVien.Rows[index];
            txtMaSV.Text = rsv["MaSV"].ToString();
            txtHoSV.Text = rsv["HoSV"].ToString();
            txtTenSV.Text = rsv["TenSV"].ToString();
            chkGioiTinh.Checked = (Boolean)rsv["Phai"];
            dtpNgaySinh.Text = rsv["NgaySinh"].ToString();
            txtNoiSinh.Text = rsv["NoiSinh"].ToString();
            cboMaKH.Text = rsv["MaKH"].ToString();
            txtHocBong.Text = rsv["HocBong"].ToString();
            // thẻ hiện số thứ tự mẫu tin hiện hành
            lblstt.Text = (index + 1) + "/" + (tblSinhVien.Rows.Count);

            //tính tổng điểm
            txtTongDiem.Text = Tong_Diem(txtMaSV.Text).ToString("00.00 đ");
        }
        private double Tong_Diem(string MSV)
        {
            double kq = 0;
            object td = tblKetQua.Compute("sum(Diem)", "MaSV = '" + MSV + "'");
            if (td == DBNull.Value)
                kq = 0;
            else
                kq = Convert.ToDouble(td);
            return kq;
        }


        private void Nhap_Lieu_tbSinhVien()
        {
            //1. mở kết nối
            cnn.Open();
            //2. khởi tạo đối tượng command tương ứng để đọc dữ liệu từ table KHOA
            cmdSinhVien = new OleDbCommand("select * from sinhvien", cnn);
            //3.
            OleDbDataReader rkh = cmdSinhVien.ExecuteReader();
            //4.
            while (rkh.Read()) //mỗi lần đọc thì rkh trỏ đến 1 dòng trong table
            {
                DataRow r = tblSinhVien.NewRow();
                // r[0] = rkh[0];
                // r[1] = rkh[1];
                for (int i = 0; i < rkh.FieldCount; i++)
                    r[i] = rkh[i];
                tblSinhVien.Rows.Add(r);
            }
            //đóng DataReader và đối tượng kết nối
            rkh.Close();
            cnn.Close();
        }

        private void Nhap_Lieu_tbKetQua()
        {
            //1. mở kết nối
            cnn.Open();
            //2. khởi tạo đối tượng command tương ứng để đọc dữ liệu từ table KHOA
            cmdKetQua = new OleDbCommand("select * from ketqua", cnn);
            //3.
            OleDbDataReader rkh = cmdKetQua.ExecuteReader();
            //4.
            while (rkh.Read()) //mỗi lần đọc thì rkh trỏ đến 1 dòng trong table
            {
                DataRow r = tblKetQua.NewRow();
                // r[0] = rkh[0];
                // r[1] = rkh[1];
                for (int i = 0; i < rkh.FieldCount; i++)
                    r[i] = rkh[i];
                tblKetQua.Rows.Add(r);
            }
            //đóng DataReader và đối tượng kết nối
            rkh.Close();
            cnn.Close();
        }

        private void Khoi_Tao_Combo_Khoa()
        {
            cboMaKH.DisplayMember = "TenKH";
            cboMaKH.ValueMember = "MaKH";
            cboMaKH.DataSource = tblKhoa;
        }

        private void Nhap_Lieu_tbKhoa()
        {
            //1. mở kết nối
            cnn.Open();
            //2. khởi tạo đối tượng command tương ứng để đọc dữ liệu từ table KHOA
            cmdKhoa = new OleDbCommand("select * from khoa", cnn);
            //3.
            OleDbDataReader rkh = cmdKhoa.ExecuteReader();
            //4.
            while (rkh.Read()) //mỗi lần đọc thì rkh trỏ đến 1 dòng trong table
            {
                DataRow r = tblKhoa.NewRow();
                // r[0] = rkh[0];
                // r[1] = rkh[1];
                for (int i = 0; i < rkh.FieldCount; i++)
                    r[i] = rkh[i];
                tblKhoa.Rows.Add(r);
            }
            //đóng DataReader và đối tượng kết nối
            rkh.Close();
            cnn.Close();
        }

        private void Moc_Noi_Quan_He_Cac_Bang()
        {
            //Tạo quan hệ giữa tblkhoa và tblsinhvien
            ds.Relations.Add("FK_KH_SV", ds.Tables["KHOA"].Columns["MaKH"], ds.Tables["SINHVIEN"].Columns["MaKH"], true);
            //Tạo quan hệ giữa tblsinhvien và tblketqua
            ds.Relations.Add("FK_SV_KQ", ds.Tables["SINHVIEN"].Columns["MaSV"], ds.Tables["KETQUA"].Columns["MaSV"], true);

            //Loại bỏ cascade delete trong quan hệ
            ds.Relations["FK_KH_SV"].ChildKeyConstraint.DeleteRule = Rule.None;
            ds.Relations["FK_SV_KQ"].ChildKeyConstraint.DeleteRule = Rule.None;
        }

        private void btnTruoc_Click(object sender, EventArgs e)
        {
            int stt;
            if (!int.TryParse(lblstt.Text.Split('/')[0], out stt))
            {
                MessageBox.Show("Giá trị STT không hợp lệ!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (stt > 1) // stt is 1-based
            {
                stt--;
                Gan_Du_Lieu(stt - 1); // Convert to 0-based index for Gan_Du_Lieu
            }
            else
            {
                MessageBox.Show("Đang ở bản ghi đầu tiên!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void btnSau_Click(object sender, EventArgs e)
        {
            int stt;
            if (!int.TryParse(lblstt.Text.Split('/')[0], out stt))
            {
                MessageBox.Show("Giá trị STT không hợp lệ!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (stt < tblSinhVien.Rows.Count) // còn bản ghi tiếp theo (stt is 1-based)
            {
                stt++;
                Gan_Du_Lieu(stt - 1); // Convert to 0-based index for Gan_Du_Lieu
            }
            else
            {
                MessageBox.Show("Đang ở bản ghi cuối cùng!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnThem_Click(object sender, EventArgs e)
        {
            txtMaSV.ReadOnly = false;
            foreach (Control ctl in this.Controls)
                if (ctl is TextBox)
                    (ctl as TextBox).Clear();
                else if (ctl is CheckBox)
                    (ctl as CheckBox).Checked = true;
                else if (ctl is ComboBox)
                    (ctl as ComboBox).SelectedIndex = 0;
                else if (ctl is DateTimePicker)
                    (ctl as DateTimePicker).Value = new DateTime(2007, 1, 1);
            txtMaSV.Focus();

        }

        private void btnHuy_Click(object sender, EventArgs e)
        {
            DataRow rsv = tblSinhVien.Rows.Find(txtMaSV.Text);

            // Cần kiểm tra: Nếu RSV tồn tại những dòng liên quan trong tblKetQua => Không cho xóa
            // Ngược lại thì cho xóa
            // Sử dụng hàm GetChildRows để kiểm tra những dòng liên quan có tồn tại hay không?
            // Giá trị trả về của hàm này là 1 mảng
            DataRow[] mang_dong_lien_quan = rsv.GetChildRows("FK_SV_KQ");
            if (mang_dong_lien_quan.Length > 0) // Có tồn tại
            {
                MessageBox.Show("Sinh viên này đã có kết quả, không xóa được");
            }
            else
            {
                DialogResult tl = MessageBox.Show("Bạn có muốn xóa sinh viên này không?",
                                                  "Cẩn thận",
                                                  MessageBoxButtons.YesNo,
                                                  MessageBoxIcon.Question);
                if (tl == DialogResult.Yes)
                {
                    // Xóa sinh viên
                    rsv.Delete();
                    // Cập nhật lại hiển thị
                    int currentIndex = int.Parse(lblstt.Text) - 1;
                    if (currentIndex >= tblSinhVien.Rows.Count)
                        currentIndex = tblSinhVien.Rows.Count - 1;
                    if (currentIndex < 0)
                        currentIndex = 0;

                    if (tblSinhVien.Rows.Count > 0)
                    {
                        lblstt.Text = currentIndex.ToString();
                        Gan_Du_Lieu(currentIndex);
                    }
                    else
                    {
                        // Không còn sinh viên nào
                        foreach (Control ctl in this.Controls)
                        {
                            if (ctl is TextBox)
                                (ctl as TextBox).Clear();
                            else if (ctl is CheckBox)
                                (ctl as CheckBox).Checked = false;
                            else if (ctl is ComboBox)
                                (ctl as ComboBox).SelectedIndex = -1;
                        }
                        lblstt.Text = "0/0";
                    }
                }
            }
        }

        private void btnGhi_Click(object sender, EventArgs e)
        {
            if (txtMaSV.ReadOnly == true) // Ghi khi sửa dữ liệu
            {
                // Xác định dòng đang sửa
                DataRow rsv = tblSinhVien.Rows.Find(txtMaSV.Text);
                // Tiến hành sửa
                rsv["HoSV"] = txtHoSV.Text;
                rsv["TenSV"] = txtTenSV.Text;
                rsv["Phai"] = chkGioiTinh.Checked;
                rsv["NgaySinh"] = dtpNgaySinh.Text;
                rsv["NoiSinh"] = txtNoiSinh.Text;
                rsv["MaKH"] = cboMaKH.SelectedValue.ToString();
                rsv["HocBong"] = txtHocBong.Text;
            }
            else // Ghi khi thêm mới
            {
                // Kiểm tra khóa chính có bị trùng không?
                DataRow rsv = tblSinhVien.Rows.Find(txtMaSV.Text);
                if (rsv != null) // Đã trùng khóa chính
                {
                    MessageBox.Show("Trùng khóa chính. Nhập lại Mã Sinh Viên");
                    txtMaSV.Clear();
                    txtMaSV.Focus();
                    return;
                }

                // Tạo mới sinh viên
                rsv = tblSinhVien.NewRow();
                rsv["MaSV"] = txtMaSV.Text;
                rsv["HoSV"] = txtHoSV.Text;
                rsv["TenSV"] = txtTenSV.Text;
                rsv["Phai"] = chkGioiTinh.Checked;
                rsv["NgaySinh"] = dtpNgaySinh.Text;
                rsv["NoiSinh"] = txtNoiSinh.Text;
                rsv["MaKH"] = cboMaKH.SelectedValue.ToString();
                rsv["HocBong"] = txtHocBong.Text;
                tblSinhVien.Rows.Add(rsv);
                txtMaSV.ReadOnly = true;
            }
        }

        private void btnKhong_Click(object sender, EventArgs e)
        {
            int stt;
            if (int.TryParse(lblstt.Text.Split('/')[0], out stt))
            {
                Gan_Du_Lieu(stt - 1); // Convert to 0-based index
            }
            txtMaSV.ReadOnly = true;
        }

        private void Tao_cau_Truc_Cac_Bang()
        {
            tblKhoa.Columns.Add("MaKH", typeof(string));
            tblKhoa.Columns.Add("TenKH", typeof(string));
            //Tạo khoá chính cho tblKhoa
            tblKhoa.PrimaryKey = new DataColumn[] { tblKhoa.Columns["MaKH"] };

            //Tạo cấu trúc cho DataTable tương ứng với bảng SINHVIEN
            tblSinhVien.Columns.Add("MaSV", typeof(string));
            tblSinhVien.Columns.Add("HoSV", typeof(string));
            tblSinhVien.Columns.Add("TenSV", typeof(string));
            tblSinhVien.Columns.Add("Phai", typeof(Boolean));
            tblSinhVien.Columns.Add("NgaySinh", typeof(DateTime));
            tblSinhVien.Columns.Add("NoiSinh", typeof(string));
            tblSinhVien.Columns.Add("MaKH", typeof(string));
            tblSinhVien.Columns.Add("HocBong", typeof(double));
            //Tạo khoá chính cho tblSinhVien
            tblSinhVien.PrimaryKey = new DataColumn[] { tblSinhVien.Columns["MaSV"] };

            //Tạo cấu trúc cho DataTable tương ứng với bảng KETQUA
            tblKetQua.Columns.Add("MaSV", typeof(string));
            tblKetQua.Columns.Add("MaMH", typeof(string));
            tblKetQua.Columns.Add("Diem", typeof(double));
            //Tạo khoá chính cho tblKetQua
            tblKetQua.PrimaryKey = new DataColumn[] { tblKetQua.Columns["MaSV"], tblKetQua.Columns["MaMH"] };

            //Thêm các DataTable vào Dataset
            ds.Tables.Add(tblKhoa);
            ds.Tables.Add(tblSinhVien);
            ds.Tables.Add(tblKetQua);
        }

        public Form1()
        {
            InitializeComponent();
        }

        
    }
}
