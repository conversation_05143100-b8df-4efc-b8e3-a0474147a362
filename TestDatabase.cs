using System;
using System.Data.OleDb;

class TestDatabase
{
    static void Main()
    {
        string strcon1 = @"Provider=Microsoft.Jet.OLEDB.4.0;Data Source=..\DATA\QLSV.mdb";
        string strcon2 = @"Provider=Microsoft.ACE.OLEDB.12.0;Data Source=..\DATA\QLSV.accdb";
        
        Console.WriteLine("Testing database connections...");
        
        // Test connection 1
        try
        {
            using (OleDbConnection cnn = new OleDbConnection(strcon1))
            {
                cnn.Open();
                Console.WriteLine("✓ Connection 1 (Jet.OLEDB.4.0 with .mdb) successful!");
                
                OleDbCommand cmd = new OleDbCommand("SELECT COUNT(*) FROM sinhvien", cnn);
                int count = (int)cmd.ExecuteScalar();
                Console.WriteLine($"  Found {count} students in database");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Connection 1 failed: {ex.Message}");
        }
        
        // Test connection 2
        try
        {
            using (OleDbConnection cnn = new OleDbConnection(strcon2))
            {
                cnn.Open();
                Console.WriteLine("✓ Connection 2 (ACE.OLEDB.12.0 with .accdb) successful!");
                
                OleDbCommand cmd = new OleDbCommand("SELECT COUNT(*) FROM sinhvien", cnn);
                int count = (int)cmd.ExecuteScalar();
                Console.WriteLine($"  Found {count} students in database");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Connection 2 failed: {ex.Message}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
