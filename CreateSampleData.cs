using System;
using System.Data;
using System.Windows.Forms;

namespace BT01_TruyXuatDulieu
{
    public partial class Form1 : Form
    {
        // Ph<PERSON><PERSON>ng thức tạo dữ liệu mẫu khi không kết nối được database
        private void Tao_Du_Lieu_Mau()
        {
            try
            {
                // Tạo dữ liệu mẫu cho bảng Khoa
                DataRow rKhoa1 = tblKhoa.NewRow();
                rKhoa1["MaKH"] = "CNTT";
                rKhoa1["TenKH"] = "Công nghệ thông tin";
                tblKhoa.Rows.Add(rKhoa1);

                DataRow rKhoa2 = tblKhoa.NewRow();
                rKhoa2["MaKH"] = "KT";
                rKhoa2["TenKH"] = "Kế toán";
                tblKhoa.Rows.Add(rKhoa2);

                DataRow rKhoa3 = tblKhoa.NewRow();
                rKhoa3["MaKH"] = "QT";
                rKhoa3["TenKH"] = "Quản trị kinh doanh";
                tblKhoa.Rows.Add(rKhoa3);

                // Tạo dữ liệu mẫu cho bảng Sinh viên
                DataRow rSV1 = tblSinhVien.NewRow();
                rSV1["MaSV"] = "SV001";
                rSV1["HoSV"] = "Nguyễn Văn";
                rSV1["TenSV"] = "An";
                rSV1["Phai"] = true;
                rSV1["NgaySinh"] = new DateTime(2000, 1, 15);
                rSV1["NoiSinh"] = "Hà Nội";
                rSV1["MaKH"] = "CNTT";
                rSV1["HocBong"] = 1000000;
                tblSinhVien.Rows.Add(rSV1);

                DataRow rSV2 = tblSinhVien.NewRow();
                rSV2["MaSV"] = "SV002";
                rSV2["HoSV"] = "Trần Thị";
                rSV2["TenSV"] = "Bình";
                rSV2["Phai"] = false;
                rSV2["NgaySinh"] = new DateTime(2001, 3, 20);
                rSV2["NoiSinh"] = "TP.HCM";
                rSV2["MaKH"] = "KT";
                rSV2["HocBong"] = 800000;
                tblSinhVien.Rows.Add(rSV2);

                DataRow rSV3 = tblSinhVien.NewRow();
                rSV3["MaSV"] = "SV003";
                rSV3["HoSV"] = "Lê Văn";
                rSV3["TenSV"] = "Cường";
                rSV3["Phai"] = true;
                rSV3["NgaySinh"] = new DateTime(1999, 12, 10);
                rSV3["NoiSinh"] = "Đà Nẵng";
                rSV3["MaKH"] = "QT";
                rSV3["HocBong"] = 1200000;
                tblSinhVien.Rows.Add(rSV3);

                // Tạo dữ liệu mẫu cho bảng Kết quả
                DataRow rKQ1 = tblKetQua.NewRow();
                rKQ1["MaSV"] = "SV001";
                rKQ1["MaMH"] = "MH001";
                rKQ1["Diem"] = 8.5;
                tblKetQua.Rows.Add(rKQ1);

                DataRow rKQ2 = tblKetQua.NewRow();
                rKQ2["MaSV"] = "SV001";
                rKQ2["MaMH"] = "MH002";
                rKQ2["Diem"] = 7.0;
                tblKetQua.Rows.Add(rKQ2);

                DataRow rKQ3 = tblKetQua.NewRow();
                rKQ3["MaSV"] = "SV002";
                rKQ3["MaMH"] = "MH001";
                rKQ3["Diem"] = 9.0;
                tblKetQua.Rows.Add(rKQ3);

                DataRow rKQ4 = tblKetQua.NewRow();
                rKQ4["MaSV"] = "SV003";
                rKQ4["MaMH"] = "MH001";
                rKQ4["Diem"] = 6.5;
                tblKetQua.Rows.Add(rKQ4);

                DataRow rKQ5 = tblKetQua.NewRow();
                rKQ5["MaSV"] = "SV003";
                rKQ5["MaMH"] = "MH002";
                rKQ5["Diem"] = 8.0;
                tblKetQua.Rows.Add(rKQ5);

                MessageBox.Show("Đã tạo dữ liệu mẫu thành công!", "Thông báo");
            }
            catch (Exception ex)
            {
                MessageBox.Show("Lỗi khi tạo dữ liệu mẫu: " + ex.Message, "Lỗi");
            }
        }
    }
}
